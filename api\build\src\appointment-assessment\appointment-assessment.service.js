"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentAssessmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const appointment_assessment_entity_1 = require("./entities/appointment-assessment.entity");
const typeorm_2 = require("typeorm");
let AppointmentAssessmentService = class AppointmentAssessmentService {
    constructor(appointmentAssessmentRepository) {
        this.appointmentAssessmentRepository = appointmentAssessmentRepository;
    }
    async findAll(searchKeyword, clinicId) {
        if (searchKeyword) {
            return this.appointmentAssessmentRepository.find({
                where: {
                    name: (0, typeorm_2.ILike)(`%${searchKeyword}%`),
                    // isAddedByUser: false,
                    clinicId
                }
            });
        }
        return this.appointmentAssessmentRepository.find({
            where: { clinicId }
        });
    }
    async createNewAssessment(createAppointmentAssessmentDto, clinicId, brandId) {
        const { name, isAddedByUser = true } = createAppointmentAssessmentDto;
        const createdAssessent = await this.appointmentAssessmentRepository.save({
            name,
            isAddedByUser,
            clinicId,
            brandId
        });
        if (!createdAssessent) {
            throw new common_1.InternalServerErrorException('Failed in creating assessment.');
        }
        return createdAssessent;
    }
};
exports.AppointmentAssessmentService = AppointmentAssessmentService;
exports.AppointmentAssessmentService = AppointmentAssessmentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(appointment_assessment_entity_1.AppointmentAssessmentEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AppointmentAssessmentService);
//# sourceMappingURL=appointment-assessment.service.js.map