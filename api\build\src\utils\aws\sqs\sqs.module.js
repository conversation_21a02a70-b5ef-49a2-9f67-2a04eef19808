"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SqsModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SqsModule = void 0;
const common_1 = require("@nestjs/common");
const default_service_handler_1 = require("./handlers/default-service.handler");
const sqs_service_1 = require("./sqs.service");
const process_create_emr_handler_1 = require("./handlers/process_create_emr.handler");
const process_send_documents_handler_1 = require("./handlers/process_send_documents.handler");
const process_invoice_tasks_handler_1 = require("./handlers/process_invoice_tasks.handler");
const process_availability_update_handler_1 = require("./handlers/process_availability_update.handler");
const process_availability_maintenance_handler_1 = require("./handlers/process_availability_maintenance.handler");
const process_analytics_documents_handler_1 = require("./handlers/process_analytics_documents.handler");
const emr_module_1 = require("../../../emr/emr.module");
const send_document_service_1 = require("../../common/send-document.service");
const s3_service_1 = require("../s3/s3.service");
const ses_module_1 = require("../ses/ses.module");
const whatsapp_module_1 = require("../../whatsapp-integration/whatsapp.module");
const appointments_module_1 = require("../../../appointments/appointments.module");
const availability_module_1 = require("../../../availability/availability.module");
const typeorm_1 = require("@nestjs/typeorm");
const patient_vaccinations_entity_1 = require("../../../patient-vaccinations/entities/patient-vaccinations.entity");
const patient_entity_1 = require("../../../patients/entities/patient.entity");
const invoice_entity_1 = require("../../../invoice/entities/invoice.entity");
const clinic_product_entity_1 = require("../../../clinic-products/entities/clinic-product.entity");
const clinic_vaccination_entity_1 = require("../../../clinic-vaccinations/entities/clinic-vaccination.entity");
const clinic_medication_entity_1 = require("../../../clinic-medications/entities/clinic-medication.entity");
const clinic_consumable_entity_1 = require("../../../clinic-consumables/entities/clinic-consumable.entity");
const patient_reminder_module_1 = require("../../../patient-reminders/patient-reminder.module");
const global_reminders_module_1 = require("../../../patient-global-reminders/global-reminders.module");
const dotenv = require("dotenv");
const clinic_entity_1 = require("../../../clinics/entities/clinic.entity");
const payment_details_entity_1 = require("../../../payment-details/entities/payment-details.entity");
const owner_brand_entity_1 = require("../../../owners/entities/owner-brand.entity");
const patients_module_1 = require("../../../patients/patients.module");
const analytics_document_request_entity_1 = require("../../../analytics-sharing/entities/analytics-document-request.entity");
const analytics_module_1 = require("../../../analytics/analytics.module");
const clinic_lab_report_module_1 = require("../../../clinic-lab-report/clinic-lab-report.module");
const clinic_user_entity_1 = require("../../../clinics/entities/clinic-user.entity");
const logger_module_1 = require("../../logger/logger-module");
const appointment_details_entity_1 = require("../../../appointments/entities/appointment-details.entity");
const statement_module_1 = require("../../../statement/statement.module");
const appointment_doctor_entity_1 = require("../../../appointments/entities/appointment-doctor.entity");
const tab_activity_module_1 = require("../../../tab-activity/tab-activity.module");
const merged_invoice_document_entity_1 = require("../../../invoice/entities/merged-invoice-document.entity");
const merged_payment_receipt_document_entity_1 = require("../../../payment-details/entities/merged-payment-receipt-document.entity");
// Load environment variables
dotenv.config({ path: '.env' });
let SqsModule = SqsModule_1 = class SqsModule {
    static forRoot(isSqsEnabled) {
        console.log('isSqsEnabled', isSqsEnabled || process.env.NODE_ENV === 'development');
        if (isSqsEnabled || process.env.NODE_ENV === 'development') {
            sqs_service_1.SqsService.enableInitialization();
            return {
                module: SqsModule_1,
                imports: [
                    (0, common_1.forwardRef)(() => emr_module_1.EmrModule),
                    (0, common_1.forwardRef)(() => availability_module_1.AvailabilityModule),
                    (0, common_1.forwardRef)(() => analytics_module_1.AnalyticsModule),
                    logger_module_1.LoggerModule,
                    ses_module_1.SESModule,
                    whatsapp_module_1.WhatsappModule,
                    appointments_module_1.AppointmentsModule,
                    patient_reminder_module_1.PatientRemindersModule,
                    global_reminders_module_1.GlobalReminderModule,
                    patients_module_1.PatientsModule,
                    clinic_lab_report_module_1.ClinicLabReportModule,
                    tab_activity_module_1.TabActivityModule,
                    (0, common_1.forwardRef)(() => statement_module_1.StatementModule),
                    typeorm_1.TypeOrmModule.forFeature([
                        patient_vaccinations_entity_1.PatientVaccination,
                        patient_entity_1.Patient,
                        invoice_entity_1.InvoiceEntity,
                        clinic_product_entity_1.ClinicProductEntity,
                        clinic_vaccination_entity_1.ClinicVaccinationEntity,
                        clinic_medication_entity_1.ClinicMedicationEntity,
                        clinic_consumable_entity_1.ClinicConsumableEntity,
                        appointment_doctor_entity_1.AppointmentDoctorsEntity,
                        clinic_entity_1.ClinicEntity,
                        payment_details_entity_1.PaymentDetailsEntity,
                        owner_brand_entity_1.OwnerBrand,
                        clinic_user_entity_1.ClinicUser,
                        appointment_details_entity_1.AppointmentDetailsEntity,
                        merged_invoice_document_entity_1.MergedInvoiceDocumentEntity,
                        merged_payment_receipt_document_entity_1.MergedPaymentReceiptDocumentEntity,
                        analytics_document_request_entity_1.AnalyticsDocumentRequestEntity
                    ])
                ],
                providers: [
                    sqs_service_1.SqsService,
                    default_service_handler_1.DefaultServiceHandler,
                    process_create_emr_handler_1.ProcessEMRHandler,
                    process_send_documents_handler_1.ProcessSendDocumentsHandler,
                    process_invoice_tasks_handler_1.ProcessInvoiceTasksHandler,
                    process_availability_update_handler_1.ProcessAvailabilityUpdateHandler,
                    process_availability_maintenance_handler_1.ProcessAvailabilityMaintenanceHandler,
                    process_analytics_documents_handler_1.ProcessAnalyticsDocumentsHandler,
                    send_document_service_1.SendDocuments,
                    s3_service_1.S3Service
                ],
                exports: [
                    sqs_service_1.SqsService,
                    default_service_handler_1.DefaultServiceHandler,
                    process_create_emr_handler_1.ProcessEMRHandler,
                    process_send_documents_handler_1.ProcessSendDocumentsHandler,
                    process_invoice_tasks_handler_1.ProcessInvoiceTasksHandler,
                    process_availability_update_handler_1.ProcessAvailabilityUpdateHandler,
                    process_availability_maintenance_handler_1.ProcessAvailabilityMaintenanceHandler
                ]
            };
        }
        else {
            // If SQS is not enabled, return an empty module
            sqs_service_1.SqsService.disableInitialization();
            return {
                module: SqsModule_1,
                imports: [],
                providers: [sqs_service_1.SqsService],
                exports: [sqs_service_1.SqsService]
            };
        }
    }
};
exports.SqsModule = SqsModule;
exports.SqsModule = SqsModule = SqsModule_1 = __decorate([
    (0, common_1.Module)({
        providers: [sqs_service_1.SqsService],
        exports: [sqs_service_1.SqsService]
    })
], SqsModule);
//# sourceMappingURL=sqs.module.js.map