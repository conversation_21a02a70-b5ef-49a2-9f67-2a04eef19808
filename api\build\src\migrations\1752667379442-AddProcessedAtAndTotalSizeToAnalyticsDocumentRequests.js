"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442 = void 0;
class AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442 {
    constructor() {
        this.name = 'AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442';
    }
    async up(queryRunner) {
        await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			ADD COLUMN "total_size" bigint DEFAULT 0
		`);
        await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			ADD COLUMN "processed_at" timestamp
		`);
    }
    async down(queryRunner) {
        await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			DROP COLUMN "processed_at"
		`);
        await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			DROP COLUMN "total_size"
		`);
    }
}
exports.AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442 = AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442;
//# sourceMappingURL=1752667379442-AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests.js.map