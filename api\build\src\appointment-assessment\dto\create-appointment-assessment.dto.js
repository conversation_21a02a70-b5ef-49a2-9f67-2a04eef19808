"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointmentAssessmentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateAppointmentAssessmentDto {
    constructor() {
        this.isAddedByUser = true;
    }
}
exports.CreateAppointmentAssessmentDto = CreateAppointmentAssessmentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the lab report.',
        example: 'Blood Test',
        required: true
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'The lab report should have a name.' }),
    __metadata("design:type", String)
], CreateAppointmentAssessmentDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'If the assessment is getting added by the user. The default valus is true',
        example: true,
        required: false
    }),
    __metadata("design:type", Boolean)
], CreateAppointmentAssessmentDto.prototype, "isAddedByUser", void 0);
//# sourceMappingURL=create-appointment-assessment.dto.js.map