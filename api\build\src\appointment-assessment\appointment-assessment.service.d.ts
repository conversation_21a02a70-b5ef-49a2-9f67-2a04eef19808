import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { Repository } from 'typeorm';
import { CreateAppointmentAssessmentDto } from './dto/create-appointment-assessment.dto';
export declare class AppointmentAssessmentService {
    private readonly appointmentAssessmentRepository;
    constructor(appointmentAssessmentRepository: Repository<AppointmentAssessmentEntity>);
    findAll(searchKeyword?: string, clinicId?: string): Promise<AppointmentAssessmentEntity[]>;
    createNewAssessment(createAppointmentAssessmentDto: CreateAppointmentAssessmentDto, clinicId: string, brandId: string): Promise<{
        name: string;
        isAddedByUser: boolean;
        clinicId: string;
        brandId: string;
    } & AppointmentAssessmentEntity>;
}
