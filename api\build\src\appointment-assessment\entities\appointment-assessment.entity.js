"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentAssessmentEntity = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const common_1 = require("@nestjs/common");
let AppointmentAssessmentEntity = class AppointmentAssessmentEntity {
};
exports.AppointmentAssessmentEntity = AppointmentAssessmentEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AppointmentAssessmentEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: false }),
    __metadata("design:type", String)
], AppointmentAssessmentEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], AppointmentAssessmentEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], AppointmentAssessmentEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid', { nullable: true, name: 'created_by' }),
    __metadata("design:type", String)
], AppointmentAssessmentEntity.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], AppointmentAssessmentEntity.prototype, "createdByUser", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid', { nullable: true, name: 'updated_by' }),
    __metadata("design:type", String)
], AppointmentAssessmentEntity.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], AppointmentAssessmentEntity.prototype, "updatedByUser", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', name: 'is_added_by_user' }),
    (0, common_1.Optional)(),
    __metadata("design:type", Boolean)
], AppointmentAssessmentEntity.prototype, "isAddedByUser", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid', { name: 'clinic_id' }),
    __metadata("design:type", String)
], AppointmentAssessmentEntity.prototype, "clinicId", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid', { name: 'brand_id' }),
    __metadata("design:type", String)
], AppointmentAssessmentEntity.prototype, "brandId", void 0);
exports.AppointmentAssessmentEntity = AppointmentAssessmentEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'appointment_assessments' })
], AppointmentAssessmentEntity);
//# sourceMappingURL=appointment-assessment.entity.js.map