import { AppointmentAssessmentService } from './appointment-assessment.service';
import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { CreateAppointmentAssessmentDto } from './dto/create-appointment-assessment.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
export declare class AppointmentAssessmentController {
    private readonly appointmentAssessmentService;
    private readonly logger;
    constructor(appointmentAssessmentService: AppointmentAssessmentService, logger: <PERSON>Logger);
    getAppointmentAssessments(req: {
        user: {
            clinicId: string;
        };
    }, search?: string): Promise<AppointmentAssessmentEntity[]>;
    createNewAssessment(createAppointmentAssessmentDto: CreateAppointmentAssessmentDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<{
        name: string;
        isAddedByUser: boolean;
        clinicId: string;
        brandId: string;
    } & AppointmentAssessmentEntity>;
}
